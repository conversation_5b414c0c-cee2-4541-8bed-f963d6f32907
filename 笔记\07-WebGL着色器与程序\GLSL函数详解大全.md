# GLSL 函数详解大全

## 概述

GLSL (OpenGL Shading Language) 提供了丰富的内置函数库，这些函数经过 GPU 硬件优化，能够高效地执行各种数学运算、纹理采样、几何变换等操作。本文档详细介绍 GLSL 中的各类函数及其使用方法。

## 1. 数学函数 (Mathematical Functions)

### 1.1 基础数学函数

#### abs() - 绝对值

```glsl
float abs(float x)
vec2 abs(vec2 x)
vec3 abs(vec3 x)
vec4 abs(vec4 x)
```

**功能**: 返回参数的绝对值
**示例**:

```glsl
float result = abs(-3.5); // 返回 3.5
vec3 result = abs(vec3(-1.0, 2.0, -3.0)); // 返回 vec3(1.0, 2.0, 3.0)
```

#### sign() - 符号函数

```glsl
float sign(float x)
vec2 sign(vec2 x)
vec3 sign(vec3 x)
vec4 sign(vec4 x)
```

**功能**: 返回参数的符号，正数返回 1.0，负数返回-1.0，零返回 0.0
**示例**:

```glsl
float result = sign(-5.0); // 返回 -1.0
```

#### floor() - 向下取整

```glsl
float floor(float x)
vec2 floor(vec2 x)
vec3 floor(vec3 x)
vec4 floor(vec4 x)
```

**功能**: 返回小于或等于参数的最大整数值
**示例**:

```glsl
float result = floor(3.7); // 返回 3.0
float result2 = floor(-2.3); // 返回 -3.0
```

#### ceil() - 向上取整

```glsl
float ceil(float x)
vec2 ceil(vec2 x)
vec3 ceil(vec3 x)
vec4 ceil(vec4 x)
```

**功能**: 返回大于或等于参数的最小整数值
**示例**:

```glsl
float result = ceil(3.2); // 返回 4.0
float result2 = ceil(-2.7); // 返回 -2.0
```

#### fract() - 小数部分

```glsl
float fract(float x)
vec2 fract(vec2 x)
vec3 fract(vec3 x)
vec4 fract(vec4 x)
```

**功能**: 返回参数的小数部分，等价于 x - floor(x)
**示例**:

```glsl
float result = fract(3.7); // 返回 0.7
float result2 = fract(-2.3); // 返回 0.7 (因为 -2.3 - (-3.0) = 0.7)
```

### 1.2 幂函数与指数函数

#### pow() - 幂运算

```glsl
float pow(float x, float y)
vec2 pow(vec2 x, vec2 y)
vec3 pow(vec3 x, vec3 y)
vec4 pow(vec4 x, vec4 y)
```

**功能**: 返回 x 的 y 次幂
**示例**:

```glsl
float result = pow(2.0, 3.0); // 返回 8.0
vec3 result = pow(vec3(2.0, 3.0, 4.0), vec3(2.0)); // 返回 vec3(4.0, 9.0, 16.0)
```

#### exp() - 自然指数

```glsl
float exp(float x)
vec2 exp(vec2 x)
vec3 exp(vec3 x)
vec4 exp(vec4 x)
```

**功能**: 返回 e 的 x 次幂
**示例**:

```glsl
float result = exp(1.0); // 返回约 2.718
```

#### log() - 自然对数

```glsl
float log(float x)
vec2 log(vec2 x)
vec3 log(vec3 x)
vec4 log(vec4 x)
```

**功能**: 返回参数的自然对数
**示例**:

```glsl
float result = log(2.718); // 返回约 1.0
```

#### sqrt() - 平方根

```glsl
float sqrt(float x)
vec2 sqrt(vec2 x)
vec3 sqrt(vec3 x)
vec4 sqrt(vec4 x)
```

**功能**: 返回参数的平方根
**示例**:

```glsl
float result = sqrt(16.0); // 返回 4.0
```

#### inversesqrt() - 平方根倒数

```glsl
float inversesqrt(float x)
vec2 inversesqrt(vec2 x)
vec3 inversesqrt(vec3 x)
vec4 inversesqrt(vec4 x)
```

**功能**: 返回 1/sqrt(x)，比 1.0/sqrt(x) 更高效
**示例**:

```glsl
float result = inversesqrt(4.0); // 返回 0.5
```

### 1.3 三角函数

#### sin() - 正弦函数

```glsl
float sin(float angle)
vec2 sin(vec2 angle)
vec3 sin(vec3 angle)
vec4 sin(vec4 angle)
```

**功能**: 返回角度的正弦值（角度以弧度为单位）
**示例**:

```glsl
float result = sin(3.14159 / 2.0); // 返回约 1.0
```

#### cos() - 余弦函数

```glsl
float cos(float angle)
vec2 cos(vec2 angle)
vec3 cos(vec3 angle)
vec4 cos(vec4 angle)
```

**功能**: 返回角度的余弦值（角度以弧度为单位）
**示例**:

```glsl
float result = cos(0.0); // 返回 1.0
```

#### tan() - 正切函数

```glsl
float tan(float angle)
vec2 tan(vec2 angle)
vec3 tan(vec3 angle)
vec4 tan(vec4 angle)
```

**功能**: 返回角度的正切值（角度以弧度为单位）
**示例**:

```glsl
float result = tan(3.14159 / 4.0); // 返回约 1.0
```

#### radians() - 角度转弧度

```glsl
float radians(float degrees)
vec2 radians(vec2 degrees)
vec3 radians(vec3 degrees)
vec4 radians(vec4 degrees)
```

**功能**: 将角度转换为弧度
**示例**:

```glsl
float result = radians(180.0); // 返回约 3.14159
```

#### degrees() - 弧度转角度

```glsl
float degrees(float radians)
vec2 degrees(vec2 radians)
vec3 degrees(vec3 radians)
vec4 degrees(vec4 radians)
```

**功能**: 将弧度转换为角度
**示例**:

```glsl
float result = degrees(3.14159); // 返回约 180.0
```

## 2. 插值与混合函数

### 2.1 线性插值

#### mix() - 线性插值

```glsl
float mix(float x, float y, float a)
vec2 mix(vec2 x, vec2 y, float a)
vec3 mix(vec3 x, vec3 y, float a)
vec4 mix(vec4 x, vec4 y, float a)
```

**功能**: 返回 x _ (1-a) + y _ a 的线性插值
**示例**:

```glsl
float result = mix(0.0, 10.0, 0.5); // 返回 5.0
vec3 color = mix(vec3(1.0, 0.0, 0.0), vec3(0.0, 1.0, 0.0), 0.5); // 红绿混合
```

#### step() - 阶跃函数

```glsl
float step(float edge, float x)
vec2 step(vec2 edge, vec2 x)
vec3 step(vec3 edge, vec3 x)
vec4 step(vec4 edge, vec4 x)
```

**功能**: 如果 x < edge 返回 0.0，否则返回 1.0
**示例**:

```glsl
float result = step(0.5, 0.7); // 返回 1.0
float result2 = step(0.5, 0.3); // 返回 0.0
```

#### smoothstep() - 平滑阶跃函数

```glsl
float smoothstep(float edge0, float edge1, float x)
vec2 smoothstep(vec2 edge0, vec2 edge1, vec2 x)
vec3 smoothstep(vec3 edge0, vec3 edge1, vec3 x)
vec4 smoothstep(vec4 edge0, vec4 edge1, vec4 x)
```

**功能**: 在 edge0 和 edge1 之间进行平滑插值
**示例**:

```glsl
float result = smoothstep(0.0, 1.0, 0.5); // 返回平滑插值结果
```

### 2.2 范围限制

#### clamp() - 范围限制

```glsl
float clamp(float x, float minVal, float maxVal)
vec2 clamp(vec2 x, vec2 minVal, vec2 maxVal)
vec3 clamp(vec3 x, vec3 minVal, vec3 maxVal)
vec4 clamp(vec4 x, vec4 minVal, vec4 maxVal)
```

**功能**: 将值限制在指定范围内
**示例**:

```glsl
float result = clamp(1.5, 0.0, 1.0); // 返回 1.0
vec3 color = clamp(vec3(1.2, -0.5, 0.8), 0.0, 1.0); // 返回 vec3(1.0, 0.0, 0.8)
```

#### min() - 最小值

```glsl
float min(float x, float y)
vec2 min(vec2 x, vec2 y)
vec3 min(vec3 x, vec3 y)
vec4 min(vec4 x, vec4 y)
```

**功能**: 返回两个参数中的较小值
**示例**:

```glsl
float result = min(3.0, 5.0); // 返回 3.0
```

#### max() - 最大值

```glsl
float max(float x, float y)
vec2 max(vec2 x, vec2 y)
vec3 max(vec3 x, vec3 y)
vec4 max(vec4 x, vec4 y)
```

**功能**: 返回两个参数中的较大值
**示例**:

```glsl
float result = max(3.0, 5.0); // 返回 5.0
```

## 3. 向量函数

### 3.1 向量运算

#### length() - 向量长度

```glsl
float length(float x)
float length(vec2 x)
float length(vec3 x)
float length(vec4 x)
```

**功能**: 返回向量的长度（模）
**示例**:

```glsl
float len = length(vec3(3.0, 4.0, 0.0)); // 返回 5.0
```

#### distance() - 两点距离

```glsl
float distance(float p0, float p1)
float distance(vec2 p0, vec2 p1)
float distance(vec3 p0, vec3 p1)
float distance(vec4 p0, vec4 p1)
```

**功能**: 返回两点之间的距离
**示例**:

```glsl
float dist = distance(vec3(0.0, 0.0, 0.0), vec3(3.0, 4.0, 0.0)); // 返回 5.0
```

#### dot() - 点积

```glsl
float dot(float x, float y)
float dot(vec2 x, vec2 y)
float dot(vec3 x, vec3 y)
float dot(vec4 x, vec4 y)
```

**功能**: 返回两个向量的点积
**示例**:

```glsl
float result = dot(vec3(1.0, 0.0, 0.0), vec3(0.0, 1.0, 0.0)); // 返回 0.0
```

#### cross() - 叉积

```glsl
vec3 cross(vec3 x, vec3 y)
```

**功能**: 返回两个三维向量的叉积
**示例**:

```glsl
vec3 result = cross(vec3(1.0, 0.0, 0.0), vec3(0.0, 1.0, 0.0)); // 返回 vec3(0.0, 0.0, 1.0)
```

#### normalize() - 向量归一化

```glsl
float normalize(float x)
vec2 normalize(vec2 x)
vec3 normalize(vec3 x)
vec4 normalize(vec4 x)
```

**功能**: 返回长度为 1 的同方向向量
**示例**:

```glsl
vec3 normal = normalize(vec3(3.0, 4.0, 0.0)); // 返回 vec3(0.6, 0.8, 0.0)
```

### 3.2 向量反射与折射

#### reflect() - 反射向量

```glsl
float reflect(float I, float N)
vec2 reflect(vec2 I, vec2 N)
vec3 reflect(vec3 I, vec3 N)
vec4 reflect(vec4 I, vec4 N)
```

**功能**: 计算入射向量相对于法向量的反射向量
**示例**:

```glsl
vec3 reflected = reflect(vec3(1.0, -1.0, 0.0), vec3(0.0, 1.0, 0.0));
```

#### refract() - 折射向量

```glsl
float refract(float I, float N, float eta)
vec2 refract(vec2 I, vec2 N, float eta)
vec3 refract(vec3 I, vec3 N, float eta)
vec4 refract(vec4 I, vec4 N, float eta)
```

**功能**: 计算入射向量的折射向量
**参数**: eta 是折射率比值
**示例**:

```glsl
vec3 refracted = refract(vec3(1.0, -1.0, 0.0), vec3(0.0, 1.0, 0.0), 0.75);
```

#### faceforward() - 面向前方

```glsl
float faceforward(float N, float I, float Nref)
vec2 faceforward(vec2 N, vec2 I, vec2 Nref)
vec3 faceforward(vec3 N, vec3 I, vec3 Nref)
vec4 faceforward(vec4 N, vec4 I, vec4 Nref)
```

**功能**: 如果 dot(Nref, I) < 0 返回 N，否则返回 -N
**示例**:

```glsl
vec3 frontFacing = faceforward(normal, incident, referenceNormal);
```

## 4. 纹理采样函数

### 4.1 基础纹理采样

#### texture2D() - 2D 纹理采样

```glsl
vec4 texture2D(sampler2D sampler, vec2 coord)
vec4 texture2D(sampler2D sampler, vec2 coord, float bias)
```

**功能**: 从 2D 纹理中采样颜色值
**示例**:

```glsl
uniform sampler2D u_texture;
varying vec2 v_texCoord;

void main() {
    vec4 color = texture2D(u_texture, v_texCoord);
    gl_FragColor = color;
}
```

#### textureCube() - 立方体纹理采样

```glsl
vec4 textureCube(samplerCube sampler, vec3 coord)
vec4 textureCube(samplerCube sampler, vec3 coord, float bias)
```

**功能**: 从立方体纹理中采样颜色值
**示例**:

```glsl
uniform samplerCube u_skybox;
varying vec3 v_direction;

void main() {
    vec4 color = textureCube(u_skybox, v_direction);
    gl_FragColor = color;
}
```

### 4.2 纹理查询函数

#### textureSize() - 纹理尺寸查询

```glsl
ivec2 textureSize(sampler2D sampler, int lod)
ivec3 textureSize(sampler3D sampler, int lod)
```

**功能**: 返回指定 LOD 级别的纹理尺寸
**示例**:

```glsl
ivec2 size = textureSize(u_texture, 0); // 获取纹理的基础尺寸
```

## 5. 实用示例

### 5.1 颜色处理示例

```glsl
// 颜色混合
vec3 blendColors(vec3 color1, vec3 color2, float factor) {
    return mix(color1, color2, factor);
}

// 颜色饱和度调整
vec3 adjustSaturation(vec3 color, float saturation) {
    float gray = dot(color, vec3(0.299, 0.587, 0.114));
    return mix(vec3(gray), color, saturation);
}
```

### 5.2 光照计算示例

```glsl
// 兰伯特漫反射
float lambertDiffuse(vec3 normal, vec3 lightDir) {
    return max(0.0, dot(normalize(normal), normalize(lightDir)));
}

// Phong高光
float phongSpecular(vec3 normal, vec3 lightDir, vec3 viewDir, float shininess) {
    vec3 reflectDir = reflect(-lightDir, normal);
    return pow(max(0.0, dot(viewDir, reflectDir)), shininess);
}
```

### 5.3 动画效果示例

```glsl
// 正弦波动画
float sineWave(float time, float frequency, float amplitude) {
    return sin(time * frequency) * amplitude;
}

// 平滑脉冲
float smoothPulse(float time, float duration) {
    float t = fract(time / duration);
    return smoothstep(0.0, 0.5, t) * smoothstep(1.0, 0.5, t);
}
```

## 6. 性能优化建议

### 6.1 函数选择建议

-   使用 `inversesqrt()` 而不是 `1.0/sqrt()`
-   使用 `mix()` 进行线性插值而不是手动计算
-   使用 `clamp()` 而不是 `min(max(x, a), b)`
-   使用 `step()` 和 `smoothstep()` 创建条件逻辑

### 6.2 向量化操作

```glsl
// 好的做法：向量化操作
vec3 result = normalize(position - lightPosition);

// 避免：分量操作
float dx = position.x - lightPosition.x;
float dy = position.y - lightPosition.y;
float dz = position.z - lightPosition.z;
float len = sqrt(dx*dx + dy*dy + dz*dz);
vec3 result = vec3(dx/len, dy/len, dz/len);
```

## 7. 矩阵函数

### 7.1 矩阵变换函数

#### matrixCompMult() - 矩阵分量乘法

```glsl
mat2 matrixCompMult(mat2 x, mat2 y)
mat3 matrixCompMult(mat3 x, mat3 y)
mat4 matrixCompMult(mat4 x, mat4 y)
```

**功能**: 对应分量相乘（不是矩阵乘法）
**示例**:

```glsl
mat3 result = matrixCompMult(matrix1, matrix2); // 分量对应相乘
```

#### transpose() - 矩阵转置

```glsl
mat2 transpose(mat2 m)
mat3 transpose(mat3 m)
mat4 transpose(mat4 m)
```

**功能**: 返回矩阵的转置
**示例**:

```glsl
mat4 transposed = transpose(modelMatrix);
```

#### determinant() - 矩阵行列式

```glsl
float determinant(mat2 m)
float determinant(mat3 m)
float determinant(mat4 m)
```

**功能**: 计算矩阵的行列式
**示例**:

```glsl
float det = determinant(transformMatrix);
```

#### inverse() - 矩阵求逆

```glsl
mat2 inverse(mat2 m)
mat3 inverse(mat3 m)
mat4 inverse(mat4 m)
```

**功能**: 计算矩阵的逆矩阵
**示例**:

```glsl
mat4 invMatrix = inverse(viewMatrix);
```

## 8. 几何函数

### 8.1 导数函数

#### dFdx() - X 方向偏导数

```glsl
float dFdx(float p)
vec2 dFdx(vec2 p)
vec3 dFdx(vec3 p)
vec4 dFdx(vec4 p)
```

**功能**: 计算表达式在 X 方向的偏导数
**示例**:

```glsl
float gradient = dFdx(textureCoord.x);
```

#### dFdy() - Y 方向偏导数

```glsl
float dFdy(float p)
vec2 dFdy(vec2 p)
vec3 dFdy(vec3 p)
vec4 dFdy(vec4 p)
```

**功能**: 计算表达式在 Y 方向的偏导数
**示例**:

```glsl
float gradient = dFdy(textureCoord.y);
```

#### fwidth() - 梯度宽度

```glsl
float fwidth(float p)
vec2 fwidth(vec2 p)
vec3 fwidth(vec3 p)
vec4 fwidth(vec4 p)
```

**功能**: 返回 abs(dFdx(p)) + abs(dFdy(p))
**示例**:

```glsl
float width = fwidth(textureCoord.x);
```

## 9. 位运算函数 (GLSL ES 3.0+)

### 9.1 整数位运算

#### bitfieldExtract() - 位域提取

```glsl
int bitfieldExtract(int value, int offset, int bits)
uint bitfieldExtract(uint value, int offset, int bits)
```

**功能**: 从整数中提取指定位域
**示例**:

```glsl
int extracted = bitfieldExtract(value, 4, 8); // 提取第4-11位
```

#### bitfieldInsert() - 位域插入

```glsl
int bitfieldInsert(int base, int insert, int offset, int bits)
uint bitfieldInsert(uint base, uint insert, int offset, int bits)
```

**功能**: 将位域插入到指定位置
**示例**:

```glsl
int result = bitfieldInsert(base, insert, 4, 8);
```

#### bitfieldReverse() - 位序反转

```glsl
int bitfieldReverse(int value)
uint bitfieldReverse(uint value)
```

**功能**: 反转整数的位序
**示例**:

```glsl
int reversed = bitfieldReverse(value);
```

#### bitCount() - 位计数

```glsl
int bitCount(int value)
int bitCount(uint value)
```

**功能**: 计算设置为 1 的位数
**示例**:

```glsl
int count = bitCount(value); // 计算1的个数
```

## 10. 噪声函数

### 10.1 内置噪声

#### noise1() - 一维噪声

```glsl
float noise1(float x)
float noise1(vec2 x)
float noise1(vec3 x)
float noise1(vec4 x)
```

**功能**: 生成一维噪声值
**示例**:

```glsl
float noiseValue = noise1(position.xy);
```

#### noise2() - 二维噪声

```glsl
vec2 noise2(float x)
vec2 noise2(vec2 x)
vec2 noise2(vec3 x)
vec2 noise2(vec4 x)
```

**功能**: 生成二维噪声向量
**示例**:

```glsl
vec2 noiseVector = noise2(position.xyz);
```

### 10.2 自定义噪声函数

#### 简单随机噪声

```glsl
float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898, 78.233))) * 43758.5453123);
}
```

#### 值噪声

```glsl
float valueNoise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);

    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));

    vec2 u = f * f * (3.0 - 2.0 * f);

    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}
```

## 11. 高级应用示例

### 11.1 程序化纹理生成

#### 棋盘格纹理

```glsl
vec3 checkerboard(vec2 uv, float scale) {
    vec2 grid = floor(uv * scale);
    float checker = mod(grid.x + grid.y, 2.0);
    return vec3(checker);
}
```

#### 条纹纹理

```glsl
vec3 stripes(vec2 uv, float frequency) {
    float stripe = sin(uv.x * frequency * 3.14159);
    return vec3(smoothstep(-0.1, 0.1, stripe));
}
```

#### 圆形图案

```glsl
vec3 circles(vec2 uv, float radius, float spacing) {
    vec2 grid = fract(uv * spacing) - 0.5;
    float dist = length(grid);
    float circle = smoothstep(radius, radius - 0.01, dist);
    return vec3(circle);
}
```

### 11.2 光照模型实现

#### Blinn-Phong 光照模型

```glsl
vec3 blinnPhong(vec3 normal, vec3 lightDir, vec3 viewDir, vec3 lightColor, vec3 materialColor, float shininess) {
    // 漫反射
    float NdotL = max(0.0, dot(normal, lightDir));
    vec3 diffuse = lightColor * materialColor * NdotL;

    // 高光
    vec3 halfVector = normalize(lightDir + viewDir);
    float NdotH = max(0.0, dot(normal, halfVector));
    vec3 specular = lightColor * pow(NdotH, shininess);

    return diffuse + specular;
}
```

#### PBR 材质函数

```glsl
float distributionGGX(vec3 N, vec3 H, float roughness) {
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;

    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = 3.14159265359 * denom * denom;

    return num / denom;
}

float geometrySchlickGGX(float NdotV, float roughness) {
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0;

    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;

    return num / denom;
}

vec3 fresnelSchlick(float cosTheta, vec3 F0) {
    return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}
```

### 11.3 后处理效果

#### 高斯模糊

```glsl
vec4 gaussianBlur(sampler2D tex, vec2 uv, vec2 resolution, float radius) {
    vec4 color = vec4(0.0);
    float total = 0.0;

    for (float x = -radius; x <= radius; x++) {
        for (float y = -radius; y <= radius; y++) {
            vec2 offset = vec2(x, y) / resolution;
            float weight = exp(-(x*x + y*y) / (2.0 * radius * radius));
            color += texture2D(tex, uv + offset) * weight;
            total += weight;
        }
    }

    return color / total;
}
```

#### 边缘检测

```glsl
float edgeDetection(sampler2D tex, vec2 uv, vec2 resolution) {
    vec2 texelSize = 1.0 / resolution;

    float tl = texture2D(tex, uv + vec2(-texelSize.x, -texelSize.y)).r;
    float tm = texture2D(tex, uv + vec2(0.0, -texelSize.y)).r;
    float tr = texture2D(tex, uv + vec2(texelSize.x, -texelSize.y)).r;
    float ml = texture2D(tex, uv + vec2(-texelSize.x, 0.0)).r;
    float mm = texture2D(tex, uv).r;
    float mr = texture2D(tex, uv + vec2(texelSize.x, 0.0)).r;
    float bl = texture2D(tex, uv + vec2(-texelSize.x, texelSize.y)).r;
    float bm = texture2D(tex, uv + vec2(0.0, texelSize.y)).r;
    float br = texture2D(tex, uv + vec2(texelSize.x, texelSize.y)).r;

    float gx = -tl + tr - 2.0*ml + 2.0*mr - bl + br;
    float gy = -tl - 2.0*tm - tr + bl + 2.0*bm + br;

    return sqrt(gx*gx + gy*gy);
}
```

## 12. 调试技巧

### 12.1 可视化调试

#### 向量可视化

```glsl
vec3 visualizeVector(vec3 vector) {
    return normalize(vector) * 0.5 + 0.5; // 映射到[0,1]范围
}
```

#### 深度可视化

```glsl
vec3 visualizeDepth(float depth, float near, float far) {
    float linearDepth = (2.0 * near) / (far + near - depth * (far - near));
    return vec3(linearDepth);
}
```

#### 法线可视化

```glsl
vec3 visualizeNormal(vec3 normal) {
    return normal * 0.5 + 0.5; // 从[-1,1]映射到[0,1]
}
```

### 12.2 性能分析函数

#### 复杂度指示器

```glsl
float complexityIndicator(float operations) {
    return smoothstep(0.0, 100.0, operations); // 红色表示高复杂度
}
```

## 总结

GLSL 函数库提供了强大的 GPU 优化函数，合理使用这些函数可以：

1. 提高着色器性能
2. 简化代码逻辑
3. 实现复杂的视觉效果
4. 保证跨平台兼容性

掌握这些函数的使用方法是编写高效 GLSL 着色器的基础。特别要注意：

-   优先使用内置函数而不是自定义实现
-   合理使用向量化操作
-   注意函数的精度要求
-   考虑不同 GPU 架构的兼容性
