<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="viewport" content="width=device-width, minimal-ui, viewport-fit=cover, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
        <link rel="icon" type="image/png" href="assets/favicon.png" />

        <title>OGL • Load GLTF</title>
        <link href="assets/main.css" rel="stylesheet" />
    </head>
    <body>
        <div class="Info" style="color: #fff">
            Load GLTF (Graphics Language Transmission Format). Model by
            <a href="https://sketchfab.com/3d-models/hershel-little-america-f4d7a0ed8af2453e9adc632278c9aa50" target="_blank">VRModelFactory</a>
            <br /><a href="/ogl/examples/?src=gltf-draco-webp.html" target="_top">Draco and WebP Compressed Version</a>
        </div>
        <script type="module">
            // ========== 导入OGL库的核心组件 ==========
            // Renderer: WebGL渲染器，负责创建WebGL上下文和渲染场景
            // Camera: 相机，定义视角和投影矩阵
            // Transform: 变换节点，用于构建场景图层次结构
            // Orbit: 轨道控制器，提供鼠标交互控制相机
            // Program: 着色器程序，包含顶点着色器和片段着色器
            // GLTFLoader: GLTF格式3D模型加载器
            // Vec3: 三维向量类
            // TextureLoader: 纹理加载器
            import { Renderer, Camera, Transform, Orbit, Program, GLTFLoader, Vec3, TextureLoader } from '../src/index.js';

            // ========== PBR（基于物理的渲染）着色器定义 ==========
            // 这是一个通用的PBR着色器，支持多种材质特性和渲染技术
            const shader = {
                // ========== 顶点着色器 ==========
                // 处理顶点变换、骨骼动画、实例化渲染等
                vertex: /* glsl */ `
                    attribute vec3 position;  // 顶点位置属性

                    // ========== 条件编译：UV坐标 ==========
                    #ifdef UV
                        attribute vec2 uv;        // UV纹理坐标属性
                    #else
                        const vec2 uv = vec2(0);  // 如果没有UV，使用默认值
                    #endif

                    // ========== 条件编译：法线 ==========
                    #ifdef NORMAL
                        attribute vec3 normal;    // 顶点法线属性
                    #else
                        const vec3 normal = vec3(0);  // 如果没有法线，使用默认值
                    #endif

                    // ========== 条件编译：实例化渲染 ==========
                    #ifdef INSTANCED
                        attribute mat4 instanceMatrix;  // 实例化矩阵属性
                    #endif

                    // ========== 条件编译：骨骼动画 ==========
                    #ifdef SKINNING
                        attribute vec4 skinIndex;   // 骨骼索引（每个顶点最多4个骨骼）
                        attribute vec4 skinWeight;  // 骨骼权重（对应4个骨骼的影响权重）
                    #endif

                    // ========== 变换矩阵 ==========
                    uniform mat4 modelViewMatrix;   // 模型视图矩阵
                    uniform mat4 projectionMatrix;  // 投影矩阵
                    uniform mat4 modelMatrix;       // 模型矩阵
                    uniform mat3 normalMatrix;      // 法线矩阵

                    // ========== 骨骼动画相关 ==========
                    #ifdef SKINNING
                        uniform sampler2D boneTexture;  // 骨骼变换矩阵纹理
                        uniform int boneTextureSize;    // 骨骼纹理大小
                    #endif

                    // ========== 传递给片段着色器的变量 ==========
                    varying vec2 vUv;      // UV坐标
                    varying vec3 vNormal;  // 世界空间法线
                    varying vec3 vMPos;    // 世界空间位置
                    varying vec4 vMVPos;   // 视图空间位置

                    // ========== 骨骼动画函数 ==========
                    #ifdef SKINNING
                        // 从骨骼纹理中获取指定索引的骨骼变换矩阵
                        mat4 getBoneMatrix(const in float i) {
                            // 计算纹理坐标：每个矩阵占用4个像素（4x4矩阵的4列）
                            float j = i * 4.0;
                            float x = mod(j, float(boneTextureSize));        // X坐标
                            float y = floor(j / float(boneTextureSize));     // Y坐标

                            // 计算纹理采样的步长
                            float dx = 1.0 / float(boneTextureSize);
                            float dy = 1.0 / float(boneTextureSize);

                            // 调整Y坐标到像素中心
                            y = dy * (y + 0.5);

                            // 从纹理中读取矩阵的4列
                            vec4 v1 = texture2D(boneTexture, vec2(dx * (x + 0.5), y));  // 第1列
                            vec4 v2 = texture2D(boneTexture, vec2(dx * (x + 1.5), y));  // 第2列
                            vec4 v3 = texture2D(boneTexture, vec2(dx * (x + 2.5), y));  // 第3列
                            vec4 v4 = texture2D(boneTexture, vec2(dx * (x + 3.5), y));  // 第4列

                            return mat4(v1, v2, v3, v4);  // 构建4x4变换矩阵
                        }

                        // 对顶点位置和法线应用骨骼变换
                        void skin(inout vec4 pos, inout vec3 nml) {
                            // 获取影响当前顶点的4个骨骼的变换矩阵
                            mat4 boneMatX = getBoneMatrix(skinIndex.x);
                            mat4 boneMatY = getBoneMatrix(skinIndex.y);
                            mat4 boneMatZ = getBoneMatrix(skinIndex.z);
                            mat4 boneMatW = getBoneMatrix(skinIndex.w);

                            // ========== 更新法线 ==========
                            // 根据骨骼权重混合变换矩阵
                            mat4 skinMatrix = mat4(0.0);
                            skinMatrix += skinWeight.x * boneMatX;
                            skinMatrix += skinWeight.y * boneMatY;
                            skinMatrix += skinWeight.z * boneMatZ;
                            skinMatrix += skinWeight.w * boneMatW;
                            nml = vec4(skinMatrix * vec4(nml, 0.0)).xyz;

                            // ========== 更新位置 ==========
                            // 根据骨骼权重混合变换后的位置
                            vec4 transformed = vec4(0.0);
                            transformed += boneMatX * pos * skinWeight.x;
                            transformed += boneMatY * pos * skinWeight.y;
                            transformed += boneMatZ * pos * skinWeight.z;
                            transformed += boneMatW * pos * skinWeight.w;
                            pos = transformed;
                        }
                    #endif

                    // ========== 顶点着色器主函数 ==========
                    void main() {
                        vec4 pos = vec4(position, 1);  // 初始化顶点位置
                        vec3 nml = normal;              // 初始化法线

                        // ========== 应用骨骼动画变换 ==========
                        #ifdef SKINNING
                            skin(pos, nml);  // 对位置和法线应用骨骼变换
                        #endif

                        // ========== 应用实例化变换 ==========
                        #ifdef INSTANCED
                            pos = instanceMatrix * pos;  // 应用实例化矩阵到位置

                            // 对法线应用实例化变换（需要考虑非均匀缩放）
                            mat3 m = mat3(instanceMatrix);
                            nml /= vec3(dot(m[0], m[0]), dot(m[1], m[1]), dot(m[2], m[2]));
                            nml = m * nml;
                        #endif

                        // ========== 设置输出变量 ==========
                        vUv = uv;                    // 传递UV坐标
                        vNormal = normalize(nml);    // 传递归一化的法线

                        // 计算世界空间位置
                        vec4 mPos = modelMatrix * pos;
                        vMPos = mPos.xyz / mPos.w;   // 透视除法得到世界坐标
                        vMVPos = modelViewMatrix * pos;  // 视图空间位置

                        // 计算最终的裁剪空间位置
                        gl_Position = projectionMatrix * vMVPos;
                    }
                `,

                // ========== PBR片段着色器 ==========
                // 实现基于物理的渲染，支持金属度/粗糙度工作流
                fragment: /* glsl */ `
                    // ========== 基础变换和相机信息 ==========
                    uniform mat4 viewMatrix;      // 视图矩阵
                    uniform vec3 cameraPosition;  // 相机世界坐标

                    // ========== 基础颜色相关 ==========
                    uniform vec4 uBaseColorFactor;  // 基础颜色因子
                    uniform sampler2D tBaseColor;   // 基础颜色纹理（漫反射贴图）

                    // ========== 金属度/粗糙度相关 ==========
                    uniform sampler2D tRM;      // 粗糙度/金属度纹理（R通道=遮挡，G通道=粗糙度，B通道=金属度）
                    uniform float uRoughness;   // 粗糙度因子
                    uniform float uMetallic;    // 金属度因子

                    // ========== 法线贴图相关 ==========
                    uniform sampler2D tNormal;    // 法线纹理
                    uniform float uNormalScale;   // 法线强度缩放

                    // ========== 自发光相关 ==========
                    uniform sampler2D tEmissive;  // 自发光纹理
                    uniform vec3 uEmissive;       // 自发光因子

                    // ========== 环境遮挡相关 ==========
                    uniform sampler2D tOcclusion; // 环境遮挡纹理

                    // ========== 基于图像的光照（IBL）相关 ==========
                    uniform sampler2D tLUT;         // BRDF查找表纹理
                    uniform sampler2D tEnvDiffuse;  // 环境漫反射纹理
                    uniform sampler2D tEnvSpecular; // 环境镜面反射纹理
                    uniform float uEnvDiffuse;      // 环境漫反射强度
                    uniform float uEnvSpecular;     // 环境镜面反射强度

                    // ========== 直接光照相关 ==========
                    uniform vec3 uLightDirection;   // 光源方向
                    uniform vec3 uLightColor;       // 光源颜色

                    // ========== 透明度相关 ==========
                    uniform float uAlpha;           // 透明度
                    uniform float uAlphaCutoff;     // Alpha测试阈值

                    // ========== 从顶点着色器接收的变量 ==========
                    varying vec2 vUv;      // UV坐标
                    varying vec3 vNormal;  // 世界空间法线
                    varying vec3 vMPos;    // 世界空间位置
                    varying vec4 vMVPos;   // 视图空间位置

                    // ========== 数学常量定义 ==========
                    const float PI = 3.14159265359;           // 圆周率
                    const float RECIPROCAL_PI = 0.31830988618; // 1/π
                    const float RECIPROCAL_PI2 = 0.15915494;   // 1/(2π)
                    const float LN2 = 0.6931472;              // ln(2)

                    const float ENV_LODS = 6.0;  // 环境贴图的mipmap级别数

                    // ========== 颜色空间转换函数 ==========

                    // 将sRGB颜色转换为线性空间（用于正确的光照计算）
                    vec4 SRGBtoLinear(vec4 srgb) {
                        vec3 linOut = pow(srgb.xyz, vec3(2.2));  // 应用gamma校正
                        return vec4(linOut, srgb.w);
                    }

                    // 将RGBM编码的HDR纹理转换为线性空间
                    // RGBM格式：RGB存储颜色，M（Alpha）存储亮度倍数
                    vec4 RGBMToLinear(in vec4 value) {
                        float maxRange = 6.0;  // 最大亮度范围
                        return vec4(value.xyz * value.w * maxRange, 1.0);
                    }

                    // 将线性空间颜色转换为sRGB（用于显示）
                    vec3 linearToSRGB(vec3 color) {
                        return pow(color, vec3(1.0 / 2.2));  // 应用逆gamma校正
                    }

                    vec3 getNormal() {
                        #ifdef NORMAL_MAP
                            vec3 pos_dx = dFdx(vMPos.xyz);
                            vec3 pos_dy = dFdy(vMPos.xyz);
                            vec2 tex_dx = dFdx(vUv);
                            vec2 tex_dy = dFdy(vUv);

                            // Tangent, Bitangent
                            vec3 t = normalize(pos_dx * tex_dy.t - pos_dy * tex_dx.t);
                            vec3 b = normalize(-pos_dx * tex_dy.s + pos_dy * tex_dx.s);
                            mat3 tbn = mat3(t, b, normalize(vNormal));

                            vec3 n = texture2D(tNormal, vUv).rgb * 2.0 - 1.0;
                            n.xy *= uNormalScale;
                            vec3 normal = normalize(tbn * n);

                            // Get world normal from view normal (normalMatrix * normal)
                            // return normalize((vec4(normal, 0.0) * viewMatrix).xyz);
                            return normalize(normal);
                        #else
                            return normalize(vNormal);
                        #endif
                    }

                    vec3 specularReflection(vec3 specularEnvR0, vec3 specularEnvR90, float VdH) {
                        return specularEnvR0 + (specularEnvR90 - specularEnvR0) * pow(clamp(1.0 - VdH, 0.0, 1.0), 5.0);
                    }

                    float geometricOcclusion(float NdL, float NdV, float roughness) {
                        float r = roughness;

                        float attenuationL = 2.0 * NdL / (NdL + sqrt(r * r + (1.0 - r * r) * (NdL * NdL)));
                        float attenuationV = 2.0 * NdV / (NdV + sqrt(r * r + (1.0 - r * r) * (NdV * NdV)));
                        return attenuationL * attenuationV;
                    }

                    float microfacetDistribution(float roughness, float NdH) {
                        float roughnessSq = roughness * roughness;
                        float f = (NdH * roughnessSq - NdH) * NdH + 1.0;
                        return roughnessSq / (PI * f * f);
                    }

                    vec2 cartesianToPolar(vec3 n) {
                        vec2 uv;
                        uv.x = atan(n.z, n.x) * RECIPROCAL_PI2 + 0.5;
                        uv.y = asin(n.y) * RECIPROCAL_PI + 0.5;
                        return uv;
                    }

                    void getIBLContribution(inout vec3 diffuse, inout vec3 specular, float NdV, float roughness, vec3 n, vec3 reflection, vec3 diffuseColor, vec3 specularColor) {
                        vec3 brdf = SRGBtoLinear(texture2D(tLUT, vec2(NdV, roughness))).rgb;

                        vec3 diffuseLight = RGBMToLinear(texture2D(tEnvDiffuse, cartesianToPolar(n))).rgb;
                        diffuseLight = mix(vec3(1), diffuseLight, uEnvDiffuse);

                        // Sample 2 levels and mix between to get smoother degradation
                        float blend = roughness * ENV_LODS;
                        float level0 = floor(blend);
                        float level1 = min(ENV_LODS, level0 + 1.0);
                        blend -= level0;

                        // Sample the specular env map atlas depending on the roughness value
                        vec2 uvSpec = cartesianToPolar(reflection);
                        uvSpec.y /= 2.0;

                        vec2 uv0 = uvSpec;
                        vec2 uv1 = uvSpec;

                        uv0 /= pow(2.0, level0);
                        uv0.y += 1.0 - exp(-LN2 * level0);

                        uv1 /= pow(2.0, level1);
                        uv1.y += 1.0 - exp(-LN2 * level1);

                        vec3 specular0 = RGBMToLinear(texture2D(tEnvSpecular, uv0)).rgb;
                        vec3 specular1 = RGBMToLinear(texture2D(tEnvSpecular, uv1)).rgb;
                        vec3 specularLight = mix(specular0, specular1, blend);

                        diffuse = diffuseLight * diffuseColor;

                        // Bit of extra reflection for smooth materials
                        float reflectivity = pow((1.0 - roughness), 2.0) * 0.05;
                        specular = specularLight * (specularColor * brdf.x + brdf.y + reflectivity);
                        specular *= uEnvSpecular;
                    }

                    void main() {
                        vec4 baseColor = uBaseColorFactor;
                        #ifdef COLOR_MAP
                            baseColor *= SRGBtoLinear(texture2D(tBaseColor, vUv));
                        #endif

                        // Get base alpha
                        float alpha = baseColor.a;

                        #ifdef ALPHA_MASK
                            if (alpha < uAlphaCutoff) discard;
                        #endif

                        // RM map packed as gb = [nothing, roughness, metallic, nothing]
                        vec4 rmSample = vec4(1);
                        #ifdef RM_MAP
                            rmSample *= texture2D(tRM, vUv);
                        #endif
                        float roughness = clamp(rmSample.g * uRoughness, 0.04, 1.0);
                        float metallic = clamp(rmSample.b * uMetallic, 0.04, 1.0);

                        vec3 f0 = vec3(0.04);
                        vec3 diffuseColor = baseColor.rgb * (vec3(1.0) - f0) * (1.0 - metallic);
                        vec3 specularColor = mix(f0, baseColor.rgb, metallic);

                        vec3 specularEnvR0 = specularColor;
                        vec3 specularEnvR90 = vec3(clamp(max(max(specularColor.r, specularColor.g), specularColor.b) * 25.0, 0.0, 1.0));

                        vec3 N = getNormal();
                        vec3 V = normalize(cameraPosition - vMPos);
                        vec3 L = normalize(uLightDirection);
                        vec3 H = normalize(L + V);
                        vec3 reflection = normalize(reflect(-V, N));

                        float NdL = clamp(dot(N, L), 0.001, 1.0);
                        float NdV = clamp(abs(dot(N, V)), 0.001, 1.0);
                        float NdH = clamp(dot(N, H), 0.0, 1.0);
                        float LdH = clamp(dot(L, H), 0.0, 1.0);
                        float VdH = clamp(dot(V, H), 0.0, 1.0);

                        vec3 F = specularReflection(specularEnvR0, specularEnvR90, VdH);
                        float G = geometricOcclusion(NdL, NdV, roughness);
                        float D = microfacetDistribution(roughness, NdH);

                        vec3 diffuseContrib = (1.0 - F) * (diffuseColor / PI);
                        vec3 specContrib = F * G * D / (4.0 * NdL * NdV);

                        // Shading based off lights
                        vec3 color = NdL * uLightColor * (diffuseContrib + specContrib);

                        // Add lights spec to alpha for reflections on transparent surfaces (glass)
                        alpha = max(alpha, max(max(specContrib.r, specContrib.g), specContrib.b));

                        // Calculate IBL lighting
                        vec3 diffuseIBL;
                        vec3 specularIBL;
                        getIBLContribution(diffuseIBL, specularIBL, NdV, roughness, N, reflection, diffuseColor, specularColor);

                        // Add IBL on top of color
                        color += diffuseIBL + specularIBL;

                        // Add IBL spec to alpha for reflections on transparent surfaces (glass)
                        alpha = max(alpha, max(max(specularIBL.r, specularIBL.g), specularIBL.b));

                        #ifdef OCC_MAP
                            // TODO: figure out how to apply occlusion
                            // color *= SRGBtoLinear(texture2D(tOcclusion, vUv)).rgb;
                        #endif

                        #ifdef EMISSIVE_MAP
                            vec3 emissive = SRGBtoLinear(texture2D(tEmissive, vUv)).rgb * uEmissive;
                            color += emissive;
                        #endif

                        // Convert to sRGB to display
                        gl_FragColor.rgb = linearToSRGB(color);

                        // Apply uAlpha uniform at the end to overwrite any specular additions on transparent surfaces
                        gl_FragColor.a = alpha * uAlpha;
                    }
                `,
            };

            {
                // ========== 初始化WebGL渲染器 ==========
                // dpr: 2 表示设备像素比为2，提供高分辨率渲染
                const renderer = new Renderer({ dpr: 2 });
                const gl = renderer.gl; // 获取WebGL上下文
                document.body.appendChild(gl.canvas); // 将canvas添加到页面
                gl.clearColor(0.1, 0.1, 0.1, 1); // 设置背景色为深灰色

                // ========== 初始化相机 ==========
                // 设置近裁剪面和远裁剪面，适合大型3D模型
                const camera = new Camera(gl, { near: 1, far: 1000 });
                // camera.position.set(60, 25, -60);  // 备用相机位置
                camera.position.set(30, 15, -30); // 设置相机初始位置

                // ========== 创建轨道控制器 ==========
                // 提供鼠标交互功能：拖拽旋转、滚轮缩放、右键平移
                const controls = new Orbit(camera);
                // controls.target.y = 25;  // 备用目标点设置

                // ========== 窗口大小调整处理 ==========
                function resize() {
                    // 设置渲染器尺寸为窗口大小
                    renderer.setSize(window.innerWidth, window.innerHeight);
                    // 更新相机的宽高比以匹配新的窗口尺寸
                    camera.perspective({ aspect: gl.canvas.width / gl.canvas.height });
                }
                window.addEventListener('resize', resize, false);
                resize(); // 初始化时调用一次

                // ========== 创建场景根节点 ==========
                const scene = new Transform();

                // ========== GLTF模型变量 ==========
                let gltf; // 存储加载的GLTF模型数据

                // ========== 加载PBR渲染所需的公共纹理 ==========
                // 这些纹理被所有材质共享，用于基于图像的光照（IBL）

                // BRDF查找表：预计算的双向反射分布函数数据
                const lutTexture = TextureLoader.load(gl, {
                    src: 'assets/pbr/lut.png',
                });

                // 环境漫反射纹理：用于环境光的漫反射计算
                const envDiffuseTexture = TextureLoader.load(gl, {
                    src: 'assets/sunset-diffuse-RGBM.png',
                });

                // 环境镜面反射纹理：用于环境光的镜面反射计算
                const envSpecularTexture = TextureLoader.load(gl, {
                    src: 'assets/sunset-specular-RGBM.png',
                });

                {
                    // ========== 初始化应用程序 ==========
                    loadInitial(); // 加载默认模型
                    handlers(); // 设置拖拽处理器
                }

                // ========== 加载默认GLTF模型 ==========
                async function loadInitial() {
                    // 加载预设的背包模型（Hershel背包）
                    gltf = await GLTFLoader.load(gl, `assets/gltf/hershel.glb`);
                    addGLTF(gltf); // 将模型添加到场景
                }

                // ========== 设置拖拽事件处理器 ==========
                // 允许用户拖拽GLTF文件到canvas上进行加载
                function handlers() {
                    gl.canvas.addEventListener('dragover', over); // 拖拽悬停事件
                    gl.canvas.addEventListener('drop', drop); // 拖拽释放事件
                }

                // ========== 拖拽悬停处理 ==========
                function over(e) {
                    e.preventDefault(); // 阻止默认行为，允许拖拽
                }

                // ========== 拖拽释放处理 ==========
                function drop(e) {
                    e.preventDefault(); // 阻止默认行为
                    const file = e.dataTransfer.files[0]; // 获取拖拽的文件
                    const reader = new FileReader();
                    reader.readAsArrayBuffer(file); // 以ArrayBuffer格式读取文件

                    reader.onload = async function (e) {
                        let desc;
                        const textDecoder = new TextDecoder();

                        // ========== 检测文件格式 ==========
                        // 检查文件头部是否为'glTF'（GLB二进制格式）
                        if (textDecoder.decode(new Uint8Array(e.target.result, 0, 4)) === 'glTF') {
                            // GLB格式：解包二进制数据
                            desc = GLTFLoader.unpackGLB(e.target.result);
                        } else {
                            // GLTF格式：解析JSON数据
                            desc = JSON.parse(textDecoder.decode(e.target.result));
                        }

                        const dir = ''; // 资源目录（空字符串表示相对路径）
                        // 解析GLTF数据并创建3D对象
                        gltf = await GLTFLoader.parse(gl, desc, dir);
                        addGLTF(gltf); // 将新模型添加到场景
                    };
                }

                function addGLTF(gltf) {
                    scene.children.forEach((child) => child.setParent(null));
                    console.log(gltf);

                    const s = gltf.scene || gltf.scenes[0];
                    s.forEach((root) => {
                        root.setParent(scene);
                        root.traverse((node) => {
                            if (node.program) {
                                node.program = createProgram(node);
                            }
                        });
                    });

                    // Calculate world matrices for bounds
                    scene.updateMatrixWorld();

                    // Calculate rough world bounds to update camera
                    const min = new Vec3(+Infinity);
                    const max = new Vec3(-Infinity);
                    const center = new Vec3();
                    const scale = new Vec3();

                    const boundsMin = new Vec3();
                    const boundsMax = new Vec3();
                    const boundsCenter = new Vec3();
                    const boundsScale = new Vec3();

                    gltf.meshes.forEach((group) => {
                        group.primitives.forEach((mesh) => {
                            if (!mesh.parent) return; // Skip unattached

                            // TODO: for skins, go over joints, not mesh
                            // if (mesh instanceof GLTFSkin) return; // Skip skinned geometry
                            if (!mesh.geometry.bounds) mesh.geometry.computeBoundingSphere();

                            boundsCenter.copy(mesh.geometry.bounds.center).applyMatrix4(mesh.worldMatrix);

                            // Get max world scale axis
                            mesh.worldMatrix.getScaling(boundsScale);
                            const radiusScale = Math.max(Math.max(boundsScale[0], boundsScale[1]), boundsScale[2]);
                            const radius = mesh.geometry.bounds.radius * radiusScale;

                            boundsMin.set(-radius).add(boundsCenter);
                            boundsMax.set(+radius).add(boundsCenter);

                            // Apply world matrix to bounds
                            for (let i = 0; i < 3; i++) {
                                min[i] = Math.min(min[i], boundsMin[i]);
                                max[i] = Math.max(max[i], boundsMax[i]);
                            }
                        });
                    });
                    scale.sub(max, min);
                    const maxRadius = Math.max(Math.max(scale[0], scale[1]), scale[2]) * 0.5;
                    center.add(min, max).divide(2);

                    camera.position
                        .set(1, 0.5, -1)
                        .normalize()
                        .multiply(maxRadius * 2.5)
                        .add(center);
                    controls.target.copy(center);
                    controls.forcePosition();
                    const far = maxRadius * 5;
                    const near = far * 0.001;
                    camera.perspective({ near, far });
                }

                function createProgram(node) {
                    const gltf = node.program.gltfMaterial || {};
                    let { vertex, fragment } = shader;

                    const vertexPrefix = renderer.isWebgl2
                        ? /* glsl */ `#version 300 es
                        #define attribute in
                        #define varying out
                        #define texture2D texture
                    `
                        : ``;

                    const fragmentPrefix = renderer.isWebgl2
                        ? /* glsl */ `#version 300 es
                        precision highp float;
                        #define varying in
                        #define texture2D texture
                        #define gl_FragColor FragColor
                        out vec4 FragColor;
                    `
                        : /* glsl */ `#extension GL_OES_standard_derivatives : enable
                        precision highp float;
                    `;

                    let defines = `
                        ${node.geometry.attributes.uv ? `#define UV` : ``}
                        ${node.geometry.attributes.normal ? `#define NORMAL` : ``}
                        ${node.geometry.isInstanced ? `#define INSTANCED` : ``}
                        ${node.boneTexture ? `#define SKINNING` : ``}
                        ${gltf.alphaMode === 'MASK' ? `#define ALPHA_MASK` : ``}
                        ${gltf.baseColorTexture ? `#define COLOR_MAP` : ``}
                        ${gltf.normalTexture ? `#define NORMAL_MAP` : ``}
                        ${gltf.metallicRoughnessTexture ? `#define RM_MAP` : ``}
                        ${gltf.occlusionTexture ? `#define OCC_MAP` : ``}
                        ${gltf.emissiveTexture ? `#define EMISSIVE_MAP` : ``}
                    `;

                    vertex = vertexPrefix + defines + vertex;
                    fragment = fragmentPrefix + defines + fragment;

                    const program = new Program(gl, {
                        vertex,
                        fragment,
                        uniforms: {
                            uBaseColorFactor: { value: gltf.baseColorFactor || [1, 1, 1, 1] },
                            tBaseColor: { value: gltf.baseColorTexture ? gltf.baseColorTexture.texture : null },

                            tRM: { value: gltf.metallicRoughnessTexture ? gltf.metallicRoughnessTexture.texture : null },
                            uRoughness: { value: gltf.roughnessFactor !== undefined ? gltf.roughnessFactor : 1 },
                            uMetallic: { value: gltf.metallicFactor !== undefined ? gltf.metallicFactor : 1 },

                            tNormal: { value: gltf.normalTexture ? gltf.normalTexture.texture : null },
                            uNormalScale: { value: gltf.normalTexture ? gltf.normalTexture.scale || 1 : 1 },

                            tOcclusion: { value: gltf.occlusionTexture ? gltf.occlusionTexture.texture : null },

                            tEmissive: { value: gltf.emissiveTexture ? gltf.emissiveTexture.texture : null },
                            uEmissive: { value: gltf.emissiveFactor || [0, 0, 0] },

                            tLUT: { value: lutTexture },
                            tEnvDiffuse: { value: envDiffuseTexture },
                            tEnvSpecular: { value: envSpecularTexture },
                            uEnvDiffuse: { value: 0.5 },
                            uEnvSpecular: { value: 0.5 },

                            uLightDirection: { value: new Vec3(0, 1, 1) },
                            uLightColor: { value: new Vec3(2.5) },

                            uAlpha: { value: 1 },
                            uAlphaCutoff: { value: gltf.alphaCutoff },
                        },
                        transparent: gltf.alphaMode === 'BLEND',
                        cullFace: gltf.doubleSided ? false : gl.BACK,
                    });

                    return program;
                }

                requestAnimationFrame(update);
                function update() {
                    requestAnimationFrame(update);

                    // Play first animation
                    if (gltf && gltf.animations && gltf.animations.length) {
                        let { animation } = gltf.animations[0];
                        animation.elapsed += 0.01;
                        animation.update();
                    }

                    controls.update();
                    renderer.render({ scene, camera, sort: false, frustumCull: false });
                }
            }
        </script>
    </body>
</html>
